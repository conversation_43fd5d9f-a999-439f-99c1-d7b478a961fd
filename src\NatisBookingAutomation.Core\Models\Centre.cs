namespace NatisBookingAutomation.Core.Models;

public class Centre
{
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Any booking date times that were found historically for this centre.
    /// This can be used to drive analytics on when slots are typically found and how many to expect.
    /// </summary>
    public IReadOnlyCollection<DateTime> HistoricalDateTimes { get; set; } = Array.Empty<DateTime>();

    /// Location details to assist bookers in selecting a centre that is close to them.
    public double Latitude { get; set; }
    public double Longitude { get; set; }
}
