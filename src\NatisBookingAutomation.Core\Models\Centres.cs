namespace NatisBookingAutomation.Core.Models;

public class Centres
{
    private static readonly System.Threading.Lock _lock = new ();
    private static Centres? _instance = null;

    private Centres() 
    {
        AllCentres = InitializeCentres();
    }

    public IReadOnlyList<Centre> AllCentres { get; }

    // Singleton instance
    public static Centres Instance
    {
        get
        {

            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new Centres();
                    }

                }
            }

            return _instance;
        }
    }

    private IReadOnlyList<Centre> InitializeCentres()
    {
        // This would typically come from a database or external service.
        // For this example, we'll hardcode some sample centres.
        return new List<Centre>
        {
            new Centre { Name = "Centre A", Latitude = -26.2041, Longitude = 28.0473 },
            new Centre { Name = "Centre B", Latitude = -33.9249, Longitude = 18.4241 },
            new Centre { Name = "Centre C", Latitude = -29.8587, Longitude = 31.0218 },
            // Add more centres as needed
        };
    }
}
