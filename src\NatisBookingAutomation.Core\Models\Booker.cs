namespace NatisBookingAutomation.Core.Models;

/// <summary>
/// The person for which the booking is being made.
/// </summary>
public class Booker
{
    /// <summary>
    /// The system needs the login details in order to make the booking on their behalf.
    /// e<PERSON><PERSON><PERSON> supports username or email.
    /// </summary>
    public required string Login { get; set; }
    public required string Password { get; set; }

    /// <summary>
    /// The email address to which notifications are sent.
    /// </summary>
    public required string EmailAddressForNotifications { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the login credentials are valid.
    /// Valid credentials are required to make a booking, and setup preferences.
    /// If invalid, details will be purged from the system.
    /// Details should not be purged immediately, to allow for retries in case of temporary issues.
    /// </summary>
    public bool? IsValidLogin { get; set; }
    public DateTime? LastValidationAttempt { get; set; }

    /// <summary>
    /// Minimum number of days the booker prefers between today and the booking date.
    /// I.e. 1 means tomorrow, 2 means the day after tomorrow, etc.
    /// </summary>
    public int NumberOfDaysInAdvance { get; set; } = 2;

    /// <summary>
    /// Known dates the booker is unavailable for a booking.
    /// </summary>
    public IReadOnlyList<DateOnly> UnavailableDates { get; set; } = Array.Empty<DateOnly>();

    /// <summary>
    /// List of preferred centres in order of priority.
    /// </summary>
    public IReadOnlyList<string> PreferredCentresByPriority { get; set; } = Array.Empty<string>();
}
