using NatisBookingAutomation.Core.Models;

namespace NatisBookingAutomation.Core.Interfaces;

public interface INatisBookingService
{
    Task<BookingResult> BookMotorcycleLicenseTestAsync(CancellationToken cancellationToken = default);
    Task<bool> IsLoggedInAsync(CancellationToken cancellationToken = default);
    Task<bool> LoginAsync(CancellationToken cancellationToken = default);
    Task<List<CentreAvailability>> GetAvailableCentersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Given a list of user preferences and available centres, suggest bookings
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<IReadOnlyList<BookingSuggestion>> GetBookingSuggestions(IReadOnlyList<CentreAvailability> centreAvailabilities, IReadOnlyList<Booker> bookers, CancellationToken cancellationToken = default);
}
