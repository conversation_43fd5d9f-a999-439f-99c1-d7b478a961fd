namespace NatisBookingAutomation.Core.Models;

public class CentreAvailability
{
    public required string CentreName { get; set; }
    /// <summary>
    /// Correlates with the count of DateTimes
    /// </summary>
    public int AvailableSlots { get; set; }
    public IReadOnlyCollection<DateTime> DateTimes { get; set; } = Array.Empty<DateTime>();
    public int UIDropDownOptionIndex { get; set; }
}
