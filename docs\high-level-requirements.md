
# High-level requirements

1) Ability for booker to specify which centres to consider (unspecified centres are to be avoided), and in which order of preference.
2) Ability for system to calculate the distance between booker's location and centres, so that they can see which ones are closer than others.  This should be available during centre selection.

3) Ability for booker to block out calendar based on times (e.g. avoid mornings).
4) Ability for booker to block out calendar based on day of week (e.g. avoid wednesdays).
5) Ability for booker to block out calendar based on start and end date (e.g. going on leave, other commitments).

6) The system should store booking slot details (centre name and slot date-time) that appeared, and when they appeared for the first time.
7) Ability of the system to display booking analytics for each centre, i.e. number of days upfront slots open, and average number of slots per month.
8) Ability for booker to register using an email address (no password, will use OTP and CAPTCHA).
9) Ability for system to receive (and keep) and validate the booker's eNatis credentials.
10) Ability for system to generate temporary OTP for given email address.
11) Ability for system to send emails (to be triggered during main events).

## Main system events that require e-mail communication

- Register (OTP)
- Login (OTP)
- eNatis - invalid credentials
- Changes in queue position for selected centres.
- Booking successful
