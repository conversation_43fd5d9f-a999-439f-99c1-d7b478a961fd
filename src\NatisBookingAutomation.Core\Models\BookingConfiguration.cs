namespace NatisBookingAutomation.Core.Models;

/// <summary>
/// Configuration settings for booking a driving license test.
/// </summary>
public class BookingConfiguration
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string ProfileName { get; set; } = "GERT DU PLESSIS";
    public string Province { get; set; } = "Gauteng";
    public string TestCategory { get; set; } = "03 - DRIVING LICENSE FOR MOTOR CYCLE";
    public string LicenceTestType { get; set; } = "A - MOTOR CYCLE, EXCEEDING 125 CM3";
    /// <summary>
    /// PreferredCentres are loaded from the configuration file.
    /// </summary>
    public List<string> PreferredCentres { get; set; } = new() { };
    public int MaxRetryAttempts { get; set; } = 10;
    public int RetryDelaySeconds { get; set; } = 3;
    public int PageTimeoutSeconds { get; set; } = 5;
    public bool HeadlessMode { get; set; } = false;
    public string SuccessVideoUrl { get; set; } = "https://www.youtube.com/shorts/SXHMnicI6Pg";
}
